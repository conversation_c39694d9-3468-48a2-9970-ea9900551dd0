import { create } from "zustand";
import { services } from "../services";
import { createAppError, createRecoveryActions } from "../utils/errorHandler";
import { RecoveryStrategy } from "../types/error";
import type {
  Candidate,
  CandidateStatus,
  ApplicationLog,
  StoreFilters,
} from "../types";
import type { AppError, RecoveryAction } from "../types/error";

interface CandidateStoreState {
  candidates: Candidate[];
  applicationLogs: ApplicationLog[];
  filters: StoreFilters;
  loading: boolean;
  error: AppError | null;
  recoveryActions: RecoveryAction[];
  lastFailedAction: (() => Promise<void>) | null;

  // Actions
  fetchCandidates: () => Promise<void>;
  updateCandidateStatus: (
    candidateId: string,
    status: CandidateStatus
  ) => Promise<void>;
  updateCandidateRating: (candidateId: string, rating: number) => Promise<void>;
  fetchApplicationLogs: (candidateId: string) => Promise<void>;
  addApplicationLog: (
    candidateId: string,
    status: CandidateStatus,
    notes: string
  ) => Promise<void>;
  setFilters: (filters: Partial<StoreFilters>) => void;
  clearError: () => void;
  retryLastAction: () => Promise<void>;
}

export const useCandidateStore = create<CandidateStoreState>((set, get) => ({
  candidates: [],
  applicationLogs: [],
  filters: {
    position: "",
    location: "",
    status: "",
  },
  loading: false,
  error: null,
  recoveryActions: [],
  lastFailedAction: null as (() => Promise<void>) | null,

  fetchCandidates: async () => {
    const action = async () => {
      set({ loading: true, error: null, recoveryActions: [] });
      try {
        const candidates = await services.candidate.fetchCandidates();
        set({ candidates, lastFailedAction: null });
      } catch (error: any) {
        const appError =
          error instanceof Error
            ? createAppError(error, {
                component: "CandidateStore",
                action: "fetchCandidates",
              })
            : error;

        const recoveryActions = createRecoveryActions(appError);
        // Add retry action that calls this same function
        recoveryActions.unshift({
          strategy: RecoveryStrategy.RETRY,
          label: "Retry",
          action: () => get().fetchCandidates(),
        });

        set({
          error: appError,
          recoveryActions,
          lastFailedAction: () => get().fetchCandidates(),
        });
      } finally {
        set({ loading: false });
      }
    };

    await action();
  },

  updateCandidateStatus: async (
    candidateId: string,
    status: CandidateStatus
  ) => {
    try {
      set({ error: null });
      await services.candidate.updateCandidateStatus(candidateId, status);

      // Add status change to application logs
      await get().addApplicationLog(
        candidateId,
        status,
        `Status updated to ${status}`
      );
      await get().fetchCandidates();
    } catch (error: any) {
      const appError =
        error instanceof Error
          ? createAppError(error, {
              component: "CandidateStore",
              action: "updateCandidateStatus",
            })
          : error;
      set({ error: appError });
    }
  },

  updateCandidateRating: async (candidateId: string, rating: number) => {
    try {
      set({ error: null });
      await services.candidate.updateCandidateRating(candidateId, rating);
      await get().fetchCandidates();
    } catch (error: any) {
      const appError =
        error instanceof Error
          ? createAppError(error, {
              component: "CandidateStore",
              action: "updateCandidateRating",
            })
          : error;
      set({ error: appError });
    }
  },

  fetchApplicationLogs: async (candidateId: string) => {
    try {
      set({ error: null });
      const applicationLogs = await services.candidate.fetchApplicationLogs(
        candidateId
      );
      set({ applicationLogs });
    } catch (error: any) {
      const appError =
        error instanceof Error
          ? createAppError(error, {
              component: "CandidateStore",
              action: "fetchApplicationLogs",
            })
          : error;
      set({ error: appError });
    }
  },

  addApplicationLog: async (
    candidateId: string,
    status: CandidateStatus,
    notes: string
  ) => {
    try {
      set({ error: null });
      await services.candidate.addApplicationLog(candidateId, status, notes);
      await get().fetchApplicationLogs(candidateId);
    } catch (error: any) {
      const appError =
        error instanceof Error
          ? createAppError(error, {
              component: "CandidateStore",
              action: "addApplicationLog",
            })
          : error;
      set({ error: appError });
    }
  },

  setFilters: (filters) => {
    set((state) => ({
      filters: { ...state.filters, ...filters },
    }));
  },

  clearError: () => set({ error: null, recoveryActions: [] }),

  retryLastAction: async () => {
    const { lastFailedAction } = get();
    if (lastFailedAction) {
      await lastFailedAction();
    }
  },
}));
