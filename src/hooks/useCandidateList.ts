import { useEffect, useMemo, useCallback } from "react";
import { useCandidateStore, useCommonStore } from "../stores";
import type { Candidate, StoreFilters, AppError } from "../types";

interface UseCandidateListOptions {
  archived?: boolean;
}

interface UseCandidateListReturn {
  // Data
  candidates: Candidate[];
  filteredCandidates: Candidate[];
  positions: any[];
  locations: any[];
  filters: StoreFilters;
  
  // State
  loading: boolean;
  error: AppError | null;
  
  // Actions
  setFilters: (filters: Partial<StoreFilters>) => void;
  handleRetry: () => void;
  
  // Lifecycle
  initialize: () => void;
}

export function useCandidateList({ 
  archived = false 
}: UseCandidateListOptions = {}): UseCandidateListReturn {
  const {
    candidates,
    filters,
    loading: candidateLoading,
    error: candidateError,
    fetchCandidates,
    setFilters,
  } = useCandidateStore();

  const {
    positions,
    locations,
    loading: commonLoading,
    error: commonError,
    fetchPositions,
    fetchLocations,
  } = useCommonStore();

  // Combine loading states
  const loading = candidateLoading || commonLoading;
  
  // Prioritize candidate errors over common errors
  const error = candidateError || commonError;

  // Memoize filtered candidates for performance
  const filteredCandidates = useMemo(() => {
    return candidates.filter((candidate) => {
      // Filter by archived status
      if (archived && candidate.status !== "archived") return false;
      if (!archived && candidate.status === "archived") return false;

      // Apply user filters
      if (filters.position && candidate.position_id !== filters.position)
        return false;
      if (filters.location && candidate.location_id !== filters.location)
        return false;
      if (filters.status && candidate.status !== filters.status) return false;

      return true;
    });
  }, [candidates, archived, filters]);

  // Initialize data fetching
  const initialize = useCallback(() => {
    fetchCandidates();
    fetchPositions();
    fetchLocations();
  }, [fetchCandidates, fetchPositions, fetchLocations]);

  // Retry handler for error recovery
  const handleRetry = useCallback(() => {
    initialize();
  }, [initialize]);

  // Auto-initialize on mount
  useEffect(() => {
    initialize();
  }, [initialize]);

  return {
    // Data
    candidates,
    filteredCandidates,
    positions,
    locations,
    filters,
    
    // State
    loading,
    error,
    
    // Actions
    setFilters,
    handleRetry,
    
    // Lifecycle
    initialize,
  };
}
