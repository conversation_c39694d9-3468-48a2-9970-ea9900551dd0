import React from "react";
import { useCandidateList } from "../../hooks/useCandidateList";
import { AsyncErrorBoundary } from "../ui";
import { CandidateFilters, CandidateCard } from "./";

export function CandidateList({ archived = false }) {
  const {
    filteredCandidates,
    positions,
    locations,
    filters,
    loading,
    error,
    setFilters,
    handleRetry,
  } = useCandidateList({ archived });

  return (
    <AsyncErrorBoundary
      error={error}
      loading={loading}
      onRetry={handleRetry}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold text-dark">
            {archived ? "Archived Candidates" : "All Candidates"}
          </h2>

          <CandidateFilters
            filters={filters}
            positions={positions}
            locations={locations}
            archived={archived}
            onFiltersChange={setFilters}
          />
        </div>

        {/* Candidate Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCandidates.map((candidate) => (
            <CandidateCard
              key={candidate.id}
              candidate={candidate}
            />
          ))}
        </div>
      </div>
    </AsyncErrorBoundary>
  );
}
