import React, { useEffect, useState, useCallback } from "react";
import { useAdminStore } from "../../stores";
import { AsyncErrorBoundary } from "../ui";
import { Save, User, Bell, Palette, Globe } from "lucide-react";
import type { AdminProfile, AdminPreferences } from "../../types";

export function AdminSettings() {
  const {
    adminProfile,
    adminPreferences,
    loading,
    error,
    fetchAdminProfile,
    fetchAdminPreferences,
    updateAdminProfile,
    updateAdminPreferences,
  } = useAdminStore();

  const [profileForm, setProfileForm] = useState<Partial<AdminProfile>>({});
  const [preferencesForm, setPreferencesForm] = useState<Partial<AdminPreferences>>({});
  const [saving, setSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState("");

  useEffect(() => {
    fetchAdminProfile();
    fetchAdminPreferences();
  }, [fetchAdminProfile, fetchAdminPreferences]);

  useEffect(() => {
    if (adminProfile) {
      setProfileForm({
        full_name: adminProfile.full_name,
        title: adminProfile.title || "",
        phone: adminProfile.phone || "",
      });
    }
  }, [adminProfile]);

  useEffect(() => {
    if (adminPreferences) {
      setPreferencesForm({
        email_notifications: adminPreferences.email_notifications,
        new_applications: adminPreferences.new_applications || false,
        status_updates: adminPreferences.status_updates || false,
        daily_digest: adminPreferences.daily_digest || false,
        theme: adminPreferences.theme || "system",
        language: adminPreferences.language || "en",
        timezone: adminPreferences.timezone || "UTC",
      });
    }
  }, [adminPreferences]);

  const handleProfileSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setSaveMessage("");
    
    try {
      await updateAdminProfile(profileForm);
      setSaveMessage("Profile updated successfully!");
      setTimeout(() => setSaveMessage(""), 3000);
    } catch (error) {
      console.error("Failed to update profile:", error);
    } finally {
      setSaving(false);
    }
  }, [profileForm, updateAdminProfile]);

  const handlePreferencesSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setSaveMessage("");
    
    try {
      await updateAdminPreferences(preferencesForm);
      setSaveMessage("Preferences updated successfully!");
      setTimeout(() => setSaveMessage(""), 3000);
    } catch (error) {
      console.error("Failed to update preferences:", error);
    } finally {
      setSaving(false);
    }
  }, [preferencesForm, updateAdminPreferences]);

  const handleRetry = useCallback(() => {
    fetchAdminProfile();
    fetchAdminPreferences();
  }, [fetchAdminProfile, fetchAdminPreferences]);

  return (
    <AsyncErrorBoundary
      error={error?.userMessage || null}
      loading={loading}
      onRetry={handleRetry}
    >
      <div className="space-y-8">
        <div>
          <h2 className="text-2xl font-semibold text-dark mb-2">Settings</h2>
          <p className="text-gray-600">Manage your account settings and preferences.</p>
        </div>

        {saveMessage && (
          <div className="bg-green-50 text-green-700 p-3 rounded-md">
            {saveMessage}
          </div>
        )}

        {/* Profile Settings */}
        <div className="card">
          <div className="flex items-center mb-6">
            <User className="w-5 h-5 text-champagne mr-2" />
            <h3 className="text-lg font-semibold text-dark">Profile Information</h3>
          </div>

          <form onSubmit={handleProfileSubmit} className="space-y-4">
            <div>
              <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-1">
                Full Name
              </label>
              <input
                type="text"
                id="full_name"
                value={profileForm.full_name || ""}
                onChange={(e) => setProfileForm(prev => ({ ...prev, full_name: e.target.value }))}
                className="input"
                required
              />
            </div>

            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                Job Title
              </label>
              <input
                type="text"
                id="title"
                value={profileForm.title || ""}
                onChange={(e) => setProfileForm(prev => ({ ...prev, title: e.target.value }))}
                className="input"
                placeholder="e.g., Spa Manager"
              />
            </div>

            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number
              </label>
              <input
                type="tel"
                id="phone"
                value={profileForm.phone || ""}
                onChange={(e) => setProfileForm(prev => ({ ...prev, phone: e.target.value }))}
                className="input"
                placeholder="(*************"
              />
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={saving}
                className="btn-primary disabled:opacity-50 flex items-center"
              >
                <Save className="w-4 h-4 mr-2" />
                {saving ? "Saving..." : "Save Profile"}
              </button>
            </div>
          </form>
        </div>

        {/* Notification Preferences */}
        <div className="card">
          <div className="flex items-center mb-6">
            <Bell className="w-5 h-5 text-champagne mr-2" />
            <h3 className="text-lg font-semibold text-dark">Notification Preferences</h3>
          </div>

          <form onSubmit={handlePreferencesSubmit} className="space-y-4">
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={preferencesForm.email_notifications || false}
                  onChange={(e) => setPreferencesForm(prev => ({ 
                    ...prev, 
                    email_notifications: e.target.checked 
                  }))}
                  className="rounded border-gray-300 text-champagne focus:ring-champagne"
                />
                <span className="ml-2 text-sm text-gray-700">Email notifications</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={preferencesForm.new_applications || false}
                  onChange={(e) => setPreferencesForm(prev => ({ 
                    ...prev, 
                    new_applications: e.target.checked 
                  }))}
                  className="rounded border-gray-300 text-champagne focus:ring-champagne"
                />
                <span className="ml-2 text-sm text-gray-700">New application notifications</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={preferencesForm.status_updates || false}
                  onChange={(e) => setPreferencesForm(prev => ({ 
                    ...prev, 
                    status_updates: e.target.checked 
                  }))}
                  className="rounded border-gray-300 text-champagne focus:ring-champagne"
                />
                <span className="ml-2 text-sm text-gray-700">Status update notifications</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={preferencesForm.daily_digest || false}
                  onChange={(e) => setPreferencesForm(prev => ({ 
                    ...prev, 
                    daily_digest: e.target.checked 
                  }))}
                  className="rounded border-gray-300 text-champagne focus:ring-champagne"
                />
                <span className="ml-2 text-sm text-gray-700">Daily digest email</span>
              </label>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={saving}
                className="btn-primary disabled:opacity-50 flex items-center"
              >
                <Save className="w-4 h-4 mr-2" />
                {saving ? "Saving..." : "Save Preferences"}
              </button>
            </div>
          </form>
        </div>

        {/* Appearance Settings */}
        <div className="card">
          <div className="flex items-center mb-6">
            <Palette className="w-5 h-5 text-champagne mr-2" />
            <h3 className="text-lg font-semibold text-dark">Appearance</h3>
          </div>

          <form onSubmit={handlePreferencesSubmit} className="space-y-4">
            <div>
              <label htmlFor="theme" className="block text-sm font-medium text-gray-700 mb-1">
                Theme
              </label>
              <select
                id="theme"
                value={preferencesForm.theme || "system"}
                onChange={(e) => setPreferencesForm(prev => ({ 
                  ...prev, 
                  theme: e.target.value as "light" | "dark" | "system"
                }))}
                className="input"
              >
                <option value="system">System</option>
                <option value="light">Light</option>
                <option value="dark">Dark</option>
              </select>
            </div>

            <div>
              <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-1">
                Language
              </label>
              <select
                id="language"
                value={preferencesForm.language || "en"}
                onChange={(e) => setPreferencesForm(prev => ({ ...prev, language: e.target.value }))}
                className="input"
              >
                <option value="en">English</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
              </select>
            </div>

            <div>
              <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 mb-1">
                Timezone
              </label>
              <select
                id="timezone"
                value={preferencesForm.timezone || "UTC"}
                onChange={(e) => setPreferencesForm(prev => ({ ...prev, timezone: e.target.value }))}
                className="input"
              >
                <option value="UTC">UTC</option>
                <option value="America/New_York">Eastern Time</option>
                <option value="America/Chicago">Central Time</option>
                <option value="America/Denver">Mountain Time</option>
                <option value="America/Los_Angeles">Pacific Time</option>
              </select>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={saving}
                className="btn-primary disabled:opacity-50 flex items-center"
              >
                <Save className="w-4 h-4 mr-2" />
                {saving ? "Saving..." : "Save Appearance"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AsyncErrorBoundary>
  );
}
