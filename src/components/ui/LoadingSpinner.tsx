import React from "react";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  message?: string;
  fullScreen?: boolean;
  className?: string;
}

const sizeClasses = {
  sm: "h-4 w-4",
  md: "h-8 w-8", 
  lg: "h-12 w-12",
};

const containerClasses = {
  sm: "p-2",
  md: "p-4",
  lg: "p-6",
};

export function LoadingSpinner({ 
  size = "md", 
  message, 
  fullScreen = false,
  className = ""
}: LoadingSpinnerProps) {
  const spinnerClass = `animate-spin rounded-full border-b-2 border-champagne ${sizeClasses[size]}`;
  const containerClass = fullScreen 
    ? "min-h-screen flex items-center justify-center bg-gray-50"
    : `flex items-center justify-center ${containerClasses[size]}`;

  return (
    <div className={`${containerClass} ${className}`}>
      <div className="text-center">
        <div className={`${spinnerClass} mx-auto ${message ? 'mb-2' : ''}`} />
        {message && (
          <p className={`text-gray-600 ${size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'}`}>
            {message}
          </p>
        )}
      </div>
    </div>
  );
}

// Convenience components for common use cases
export function FullScreenLoader({ message = "Loading..." }: { message?: string }) {
  return <LoadingSpinner size="lg" message={message} fullScreen />;
}

export function InlineLoader({ message }: { message?: string }) {
  return <LoadingSpinner size="sm" message={message} />;
}

export function DashboardLoader({ message = "Loading..." }: { message?: string }) {
  return <LoadingSpinner size="md" message={message} />;
}
