export interface AdminProfile {
  id: string;
  email: string;
  full_name: string;
  name?: string; // Legacy field for backward compatibility
  title?: string; // Job title
  role?: "admin" | "super_admin";
  avatar_url?: string;
  phone?: string;
  created_at: string;
  updated_at: string;
}

export interface AdminPreferences {
  id: string;
  admin_id: string;
  email_notifications: boolean;
  push_notifications?: boolean;
  new_applications?: boolean; // Notification for new applications
  status_updates?: boolean; // Notification for status updates
  daily_digest?: boolean; // Daily digest email
  theme?: "light" | "dark" | "system";
  language?: string;
  timezone?: string;
  created_at: string;
  updated_at: string;
}

export interface AdminSession {
  id: string;
  admin_id: string;
  token: string;
  expires_at: string;
  created_at: string;
  last_activity: string;
  ip_address?: string;
  user_agent?: string;
}
