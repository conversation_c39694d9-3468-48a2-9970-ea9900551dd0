export interface Position {
  id: string;
  title: string;
  description: string;
  requirements?: string[];
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Location {
  id: string;
  name: string;
  address: string;
  city?: string;
  state?: string;
  zip_code?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface StoreFilters {
  position: string;
  location: string;
  status: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  offset?: number;
}

export interface SortParams {
  field: string;
  direction: "asc" | "desc";
}

export interface SearchParams {
  query: string;
  fields?: string[];
}
